# Baggage Implementation for Session ID and Customer ID

## Overview

This implementation adds support for propagating `x-session_id` and `x-customer_id` headers through OpenTelemetry Baggage, enabling seamless tracing across services in both Spring Boot 2 and Spring Boot 3 environments.

## What Was Implemented

### 1. TraceableCloseable Class
- **Location**: `logging-starter/src/main/java/com/propero/common/logging/TraceableMdcAdapter.java` (lines 23-41)
- **Purpose**: Manages both MDC and BaggageInScope cleanup in a single closeable resource
- **Features**:
  - Implements `MDC.MDCCloseable` interface
  - Properly closes baggage scopes before MDC cleanup
  - Ensures no resource leaks

### 2. Enhanced TraceableMdcAdapter
- **Location**: `logging-starter/src/main/java/com/propero/common/logging/TraceableMdcAdapter.java`
- **Key Changes**:
  - Added imports for `io.micrometer.tracing.Baggage` and `BaggageInScope`
  - Modified `from()` method to extract session ID and customer ID from headers and add them to baggage
  - Enhanced `to()` method to propagate baggage values as headers in outgoing requests

### 3. Header to Baggage Mapping
- **Input Headers**: 
  - `x-session_id` (defined as `TraceableSupport.SESSION_ID_HEADER`)
  - `x-customer_id` (defined as `TraceableSupport.CUSTOMER_ID_HEADER`)
- **Baggage Keys**:
  - `sessionId` (defined as `TraceableSupport.SESSION_ID_BAGGAGE`)
  - `customerId` (defined as `TraceableSupport.CUSTOMER_ID_BAGGAGE`)

## How It Works

### Incoming Requests
1. When a request arrives with `x-session_id` and/or `x-customer_id` headers
2. `TraceableMdcAdapter.from()` extracts these values
3. Values are added to both MDC (for logging) and Baggage (for propagation)
4. Baggage automatically propagates through the trace context

### Outgoing Requests
1. When making outbound calls, `TraceableMdcAdapter.to()` is called
2. Current baggage values are retrieved and added as headers
3. Headers are automatically included in outgoing HTTP requests, AMQP messages, etc.

### Cleanup
1. When the `TraceableCloseable` is closed (via try-with-resources)
2. All baggage scopes are properly closed first
3. MDC is then cleaned up
4. No resource leaks occur

## Compatibility

### Spring Boot 2
- Uses OpenTelemetry baggage for propagation
- Maintains backward compatibility with existing MDC-based logging
- Works with existing tracing infrastructure

### Spring Boot 3
- Leverages native W3C Trace Context support
- Baggage propagates automatically through W3C headers
- Full compatibility with Spring Boot 3's tracing features

## Usage Examples

### Automatic Usage (Recommended)
The implementation works automatically when using existing tracing components:

```java
// In a controller or service method annotated with @Traceable
@Traceable
public void handleMessage(Object payload, MessageHeaders headers) {
    // Session ID and Customer ID are automatically available in:
    // 1. MDC for logging
    // 2. Baggage for propagation to downstream services
}
```

### Manual Usage
```java
@Autowired
private TraceableMdcAdapter adapter;

public void processRequest(HttpServletRequest request) {
    try (MDC.MDCCloseable closeable = adapter.from(request, "prefix")) {
        // Session ID and Customer ID are now in baggage
        // They will automatically propagate to downstream calls
        
        // Make downstream calls - headers will be automatically added
        restTemplate.getForObject("http://other-service/api", String.class);
    }
    // Cleanup happens automatically
}
```

## Testing

### Unit Tests Added
- `fromMessageHeadersWithSessionIdAndCustomerId()`: Tests baggage creation from message headers
- `fromHttpServletRequestWithSessionIdAndCustomerId()`: Tests baggage creation from HTTP requests
- `toPropagatesBaggageValues()`: Tests baggage propagation to outgoing headers

### Demo Test
- `BaggagePropagationDemo`: Comprehensive demonstration of the entire flow

## Files Modified

1. **TraceableMdcAdapter.java**: Enhanced with baggage support
2. **TraceableMdcAdapterTest.java**: Added comprehensive tests
3. **BaggagePropagationDemo.java**: Added demonstration test

## Benefits

1. **Automatic Propagation**: Session and customer IDs propagate automatically across service boundaries
2. **Spring Boot Compatibility**: Works with both Spring Boot 2 and 3
3. **Resource Management**: Proper cleanup prevents memory leaks
4. **Backward Compatibility**: Existing functionality remains unchanged
5. **Standards Compliant**: Uses OpenTelemetry baggage standards

## Configuration

No additional configuration is required. The implementation uses existing constants from `TraceableSupport`:

- `SESSION_ID_HEADER = "x-session_id"`
- `CUSTOMER_ID_HEADER = "x-customer_id"`
- `SESSION_ID_BAGGAGE = "sessionId"`
- `CUSTOMER_ID_BAGGAGE = "customerId"`
