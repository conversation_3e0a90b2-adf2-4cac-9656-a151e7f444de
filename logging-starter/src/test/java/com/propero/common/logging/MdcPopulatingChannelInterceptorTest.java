package com.propero.common.logging;

import com.google.common.collect.ImmutableMap;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.slf4j.MDC;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.support.GenericMessage;

import java.util.HashMap;
import java.util.Map;

import static com.propero.common.logging.TraceableSupport.FUNCTION_HEADER;
import static com.propero.common.logging.TraceableSupport.TRACE_PARENT_HEADER;
import static com.propero.common.logging.TraceableSupport.TRANSACTION_ID_HEADER;
import static org.assertj.core.api.Assertions.assertThat;

public class MdcPopulatingChannelInterceptorTest extends MdcTest {

    private final TraceableMdcAdapter mdcAdapter = new TraceableMdcAdapter(new TraceableProperties(), null);

    private MdcPopulatingChannelInterceptor populatingChannelInterceptor;
    private MdcCleaningChannelInterceptor cleaningChannelInterceptor;

    @Before
    public void setUp() {
        populatingChannelInterceptor = new MdcPopulatingChannelInterceptor(mdcAdapter);
        cleaningChannelInterceptor = new MdcCleaningChannelInterceptor();
    }

    @Test
    public void postReceive() {
        assertThat(MDC.getCopyOfContextMap()).isNullOrEmpty();

        populatingChannelInterceptor.postReceive(message(true), channel());

        assertHasHeadersAndClean();

        populatingChannelInterceptor.postReceive(message(false), channel());

        assertHasHeadersAndClean();
    }

    private void assertHasHeadersAndClean() {
        assertThat(MDC.getCopyOfContextMap().keySet()).containsExactlyInAnyOrder(FUNCTION_HEADER, TRANSACTION_ID_HEADER, TRACE_PARENT_HEADER);
        cleaningChannelInterceptor.preSend(Mockito.mock(Message.class), channel());
        assertThat(MDC.getCopyOfContextMap()).isNullOrEmpty();
    }

    private MessageChannel channel() {
        return Mockito.mock(MessageChannel.class);
    }

    private Message<?> message(boolean withTransactionId) {
        Map<String, Object> headers = new HashMap<>(ImmutableMap.of(
                FUNCTION_HEADER, "foo",
                "test-header", "test"));
        if (withTransactionId) {
            headers.put(TRANSACTION_ID_HEADER, "trx-id");
        }
        return new GenericMessage<>("test", headers);
    }
}