package com.propero.common.logging;

import io.micrometer.tracing.BaggageInScope;
import io.micrometer.tracing.Tracer;
import org.junit.Test;
import org.slf4j.MDC;
import org.springframework.messaging.MessageHeaders;

import java.io.Closeable;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Test to verify that the TraceableMdcAdapter correctly returns Closeable instances
 * and properly manages both MDC and baggage cleanup.
 */
public class TraceableCloseableTest extends MdcTest {

    private final TraceableProperties traceableProperties = new TraceableProperties();
    private final Tracer mockTracer = mock(Tracer.class);
    private final TraceableMdcAdapter adapter = new TraceableMdcAdapter(traceableProperties, mockTracer);

    @Test
    public void returnsCorrectCloseableType() {
        // Arrange
        Map<String, Object> headers = new HashMap<>();
        headers.put(TraceableSupport.SESSION_ID_HEADER, "test-session");
        headers.put(TraceableSupport.CUSTOMER_ID_HEADER, "test-customer");
        headers.put(TraceableSupport.TRANSACTION_ID_HEADER, "test-transaction");
        MessageHeaders messageHeaders = new MessageHeaders(headers);

        BaggageInScope mockSessionBaggage = mock(BaggageInScope.class);
        BaggageInScope mockCustomerBaggage = mock(BaggageInScope.class);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.SESSION_ID_BAGGAGE), eq("test-session")))
                .thenReturn(mockSessionBaggage);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.CUSTOMER_ID_BAGGAGE), eq("test-customer")))
                .thenReturn(mockCustomerBaggage);

        // Act
        Closeable closeable = adapter.from(messageHeaders);

        // Assert
        assertThat(closeable).isNotNull();
        assertThat(closeable).isInstanceOf(Closeable.class);

        // Verify MDC was populated
        assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("test-session");
        assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("test-customer");
        assertThat(MDC.get(TraceableSupport.TRANSACTION_ID_HEADER)).isEqualTo("test-transaction");

        // Close and verify cleanup
        closeable.close();
        verify(mockSessionBaggage).close();
        verify(mockCustomerBaggage).close();
    }

    @Test
    public void returnsOriginalCloseableWhenNoBaggage() {
        // Arrange - headers without session/customer ID
        Map<String, Object> headers = new HashMap<>();
        headers.put(TraceableSupport.TRANSACTION_ID_HEADER, "test-transaction");
        MessageHeaders messageHeaders = new MessageHeaders(headers);

        // Act
        Closeable closeable = adapter.from(messageHeaders);

        // Assert
        assertThat(closeable).isNotNull();
        assertThat(closeable).isInstanceOf(Closeable.class);

        // Verify MDC was populated
        assertThat(MDC.get(TraceableSupport.TRANSACTION_ID_HEADER)).isEqualTo("test-transaction");

        // Close should work without issues
        closeable.close();
    }

    @Test
    public void worksWithoutTracer() {
        // Arrange
        TraceableMdcAdapter adapterWithoutTracer = new TraceableMdcAdapter(traceableProperties, null);
        Map<String, Object> headers = new HashMap<>();
        headers.put(TraceableSupport.SESSION_ID_HEADER, "test-session");
        headers.put(TraceableSupport.CUSTOMER_ID_HEADER, "test-customer");
        headers.put(TraceableSupport.TRANSACTION_ID_HEADER, "test-transaction");
        MessageHeaders messageHeaders = new MessageHeaders(headers);

        // Act
        Closeable closeable = adapterWithoutTracer.from(messageHeaders);

        // Assert
        assertThat(closeable).isNotNull();
        assertThat(closeable).isInstanceOf(Closeable.class);

        // Verify MDC was populated (baggage functionality should be skipped)
        assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("test-session");
        assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("test-customer");
        assertThat(MDC.get(TraceableSupport.TRANSACTION_ID_HEADER)).isEqualTo("test-transaction");

        // Close should work without issues
        closeable.close();
    }
}
