package com.propero.common.logging;

import io.micrometer.tracing.Baggage;
import org.junit.Test;
import org.mockito.Mockito;
import org.slf4j.MDC;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.GenericMessage;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class TraceableMdcAdapterTest extends MdcTest {

    private final TraceableProperties traceableProperties = new TraceableProperties();
    private final TraceableMdcAdapter adapter = new TraceableMdcAdapter(traceableProperties);

    @Test
    public void toAmqpMessage() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(request.getHeader(anyString())).thenAnswer(invocation -> invocation.getArgument(0));
        adapter.from(request, "test");
        MessageProperties messageProperties = new MessageProperties();
        org.springframework.amqp.core.Message message = adapter.to(new org.springframework.amqp.core.Message(new byte[0], messageProperties));
        assertThat(message).isNotNull();
        TraceableTestSupport.getAllTraceableHeaders(traceableProperties)
                .map(messageProperties::getHeader)
                .forEach(headerValue -> assertThat(headerValue).isNotNull());
    }

    @Test
    public void toMessage() {
        TraceableTestSupport.initMdc(traceableProperties);
        GenericMessage<String> message = new GenericMessage<>("test", Collections.singletonMap("test", "test"));
        Message<?> decoratedMessage = adapter.to(message);
        assertThat(decoratedMessage.getPayload()).isSameAs(message.getPayload());
        assertThat(decoratedMessage.getHeaders()).contains(entry("test", "test"));
        TraceableTestSupport.getAllTraceableHeaders(traceableProperties)
                .forEach(header -> assertThat(decoratedMessage.getHeaders()).containsKey(header));
    }

    @Test
    public void fromMessageHeadersWithSessionIdAndCustomerId() {
        // Arrange
        Map<String, Object> headers = new HashMap<>();
        headers.put(TraceableSupport.SESSION_ID_HEADER, "test-session-123");
        headers.put(TraceableSupport.CUSTOMER_ID_HEADER, "customer-456");
        headers.put(TraceableSupport.TRANSACTION_ID_HEADER, "transaction-789");
        MessageHeaders messageHeaders = new MessageHeaders(headers);

        // Act
        try (MDC.MDCCloseable closeable = adapter.from(messageHeaders)) {
            // Assert - Check that MDC contains the values
            assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("test-session-123");
            assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("customer-456");
            assertThat(MDC.get(TraceableSupport.TRANSACTION_ID_HEADER)).isEqualTo("transaction-789");

            // Assert - Check that baggage contains the values
            assertThat(Baggage.current().get(TraceableSupport.SESSION_ID_BAGGAGE)).isEqualTo("test-session-123");
            assertThat(Baggage.current().get(TraceableSupport.CUSTOMER_ID_BAGGAGE)).isEqualTo("customer-456");
        }

        // Assert - Check that baggage is cleaned up after closing
        assertThat(Baggage.current().get(TraceableSupport.SESSION_ID_BAGGAGE)).isNull();
        assertThat(Baggage.current().get(TraceableSupport.CUSTOMER_ID_BAGGAGE)).isNull();
    }

    @Test
    public void fromHttpServletRequestWithSessionIdAndCustomerId() {
        // Arrange
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(request.getHeader(TraceableSupport.SESSION_ID_HEADER)).thenReturn("session-abc");
        when(request.getHeader(TraceableSupport.CUSTOMER_ID_HEADER)).thenReturn("customer-xyz");
        when(request.getHeader(TraceableSupport.TRANSACTION_ID_HEADER)).thenReturn("transaction-def");
        when(request.getHeader(anyString())).thenAnswer(invocation -> {
            String headerName = invocation.getArgument(0);
            if (TraceableSupport.SESSION_ID_HEADER.equals(headerName)) return "session-abc";
            if (TraceableSupport.CUSTOMER_ID_HEADER.equals(headerName)) return "customer-xyz";
            if (TraceableSupport.TRANSACTION_ID_HEADER.equals(headerName)) return "transaction-def";
            return null;
        });

        // Act
        try (MDC.MDCCloseable closeable = adapter.from(request, "test")) {
            // Assert - Check that MDC contains the values
            assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("session-abc");
            assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("customer-xyz");

            // Assert - Check that baggage contains the values
            assertThat(Baggage.current().get(TraceableSupport.SESSION_ID_BAGGAGE)).isEqualTo("session-abc");
            assertThat(Baggage.current().get(TraceableSupport.CUSTOMER_ID_BAGGAGE)).isEqualTo("customer-xyz");
        }

        // Assert - Check that baggage is cleaned up after closing
        assertThat(Baggage.current().get(TraceableSupport.SESSION_ID_BAGGAGE)).isNull();
        assertThat(Baggage.current().get(TraceableSupport.CUSTOMER_ID_BAGGAGE)).isNull();
    }

    @Test
    public void toPropagatesBaggageValues() {
        // Arrange
        Map<String, Object> headers = new HashMap<>();
        headers.put(TraceableSupport.SESSION_ID_HEADER, "session-propagate");
        headers.put(TraceableSupport.CUSTOMER_ID_HEADER, "customer-propagate");
        headers.put(TraceableSupport.TRANSACTION_ID_HEADER, "transaction-propagate");
        MessageHeaders messageHeaders = new MessageHeaders(headers);

        // Act - Set up baggage context
        try (MDC.MDCCloseable closeable = adapter.from(messageHeaders)) {
            // Create a map to capture propagated headers
            Map<String, String> propagatedHeaders = new HashMap<>();

            // Call the to method to propagate headers
            adapter.to(propagatedHeaders::put);

            // Assert - Check that baggage values are propagated as headers
            assertThat(propagatedHeaders).containsEntry(TraceableSupport.SESSION_ID_HEADER, "session-propagate");
            assertThat(propagatedHeaders).containsEntry(TraceableSupport.CUSTOMER_ID_HEADER, "customer-propagate");
            assertThat(propagatedHeaders).containsEntry(TraceableSupport.TRANSACTION_ID_HEADER, "transaction-propagate");
        }
    }
}