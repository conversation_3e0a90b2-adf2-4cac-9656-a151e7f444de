package com.propero.common.logging;

import io.micrometer.tracing.Baggage;
import io.micrometer.tracing.BaggageInScope;
import io.micrometer.tracing.Tracer;
import org.junit.Test;
import org.mockito.Mockito;
import org.slf4j.MDC;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.GenericMessage;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.entry;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TraceableMdcAdapterTest extends MdcTest {

    private final TraceableProperties traceableProperties = new TraceableProperties();
    private final Tracer mockTracer = mock(Tracer.class);
    private final TraceableMdcAdapter adapter = new TraceableMdcAdapter(traceableProperties, mockTracer);
    private final TraceableMdcAdapter adapterWithoutTracer = new TraceableMdcAdapter(traceableProperties, null);

    @Test
    public void toAmqpMessage() {
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(request.getHeader(anyString())).thenAnswer(invocation -> invocation.getArgument(0));
        adapterWithoutTracer.from(request, "test");
        MessageProperties messageProperties = new MessageProperties();
        org.springframework.amqp.core.Message message = adapterWithoutTracer.to(new org.springframework.amqp.core.Message(new byte[0], messageProperties));
        assertThat(message).isNotNull();
        TraceableTestSupport.getAllTraceableHeaders(traceableProperties)
                .map(messageProperties::getHeader)
                .forEach(headerValue -> assertThat(headerValue).isNotNull());
    }

    @Test
    public void toMessage() {
        TraceableTestSupport.initMdc(traceableProperties);
        GenericMessage<String> message = new GenericMessage<>("test", Collections.singletonMap("test", "test"));
        Message<?> decoratedMessage = adapterWithoutTracer.to(message);
        assertThat(decoratedMessage.getPayload()).isSameAs(message.getPayload());
        assertThat(decoratedMessage.getHeaders()).contains(entry("test", "test"));
        TraceableTestSupport.getAllTraceableHeaders(traceableProperties)
                .forEach(header -> assertThat(decoratedMessage.getHeaders()).containsKey(header));
    }

    @Test
    public void fromMessageHeadersWithSessionIdAndCustomerId() {
        // Arrange
        Map<String, Object> headers = new HashMap<>();
        headers.put(TraceableSupport.SESSION_ID_HEADER, "test-session-123");
        headers.put(TraceableSupport.CUSTOMER_ID_HEADER, "customer-456");
        headers.put(TraceableSupport.TRANSACTION_ID_HEADER, "transaction-789");
        MessageHeaders messageHeaders = new MessageHeaders(headers);

        // Mock baggage creation
        BaggageInScope mockSessionBaggage = mock(BaggageInScope.class);
        BaggageInScope mockCustomerBaggage = mock(BaggageInScope.class);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.SESSION_ID_BAGGAGE), eq("test-session-123")))
                .thenReturn(mockSessionBaggage);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.CUSTOMER_ID_BAGGAGE), eq("customer-456")))
                .thenReturn(mockCustomerBaggage);

        // Act
        try (MDC.MDCCloseable closeable = adapter.from(messageHeaders)) {
            // Assert - Check that MDC contains the values
            assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("test-session-123");
            assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("customer-456");
            assertThat(MDC.get(TraceableSupport.TRANSACTION_ID_HEADER)).isEqualTo("transaction-789");
        }
    }

    @Test
    public void fromHttpServletRequestWithSessionIdAndCustomerId() {
        // Arrange
        HttpServletRequest request = Mockito.mock(HttpServletRequest.class);
        when(request.getHeader(TraceableSupport.SESSION_ID_HEADER)).thenReturn("session-abc");
        when(request.getHeader(TraceableSupport.CUSTOMER_ID_HEADER)).thenReturn("customer-xyz");
        when(request.getHeader(TraceableSupport.TRANSACTION_ID_HEADER)).thenReturn("transaction-def");
        when(request.getHeader(anyString())).thenAnswer(invocation -> {
            String headerName = invocation.getArgument(0);
            if (TraceableSupport.SESSION_ID_HEADER.equals(headerName)) return "session-abc";
            if (TraceableSupport.CUSTOMER_ID_HEADER.equals(headerName)) return "customer-xyz";
            if (TraceableSupport.TRANSACTION_ID_HEADER.equals(headerName)) return "transaction-def";
            return null;
        });

        // Mock baggage creation
        BaggageInScope mockSessionBaggage = mock(BaggageInScope.class);
        BaggageInScope mockCustomerBaggage = mock(BaggageInScope.class);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.SESSION_ID_BAGGAGE), eq("session-abc")))
                .thenReturn(mockSessionBaggage);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.CUSTOMER_ID_BAGGAGE), eq("customer-xyz")))
                .thenReturn(mockCustomerBaggage);

        // Act
        try (MDC.MDCCloseable closeable = adapter.from(request, "test")) {
            // Assert - Check that MDC contains the values
            assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("session-abc");
            assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("customer-xyz");
        }
    }

    @Test
    public void backwardCompatibilityWithoutTracer() {
        // Test that the adapter works without a tracer (backward compatibility)
        Map<String, Object> headers = new HashMap<>();
        headers.put(TraceableSupport.SESSION_ID_HEADER, "session-compat");
        headers.put(TraceableSupport.CUSTOMER_ID_HEADER, "customer-compat");
        headers.put(TraceableSupport.TRANSACTION_ID_HEADER, "transaction-compat");
        MessageHeaders messageHeaders = new MessageHeaders(headers);

        // Act
        try (MDC.MDCCloseable closeable = adapterWithoutTracer.from(messageHeaders)) {
            // Assert - Check that MDC contains the values (baggage functionality should be skipped)
            assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("session-compat");
            assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("customer-compat");
            assertThat(MDC.get(TraceableSupport.TRANSACTION_ID_HEADER)).isEqualTo("transaction-compat");
        }
    }
}