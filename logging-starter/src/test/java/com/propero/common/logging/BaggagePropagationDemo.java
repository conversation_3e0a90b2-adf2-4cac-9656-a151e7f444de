package com.propero.common.logging;

import io.micrometer.tracing.Baggage;
import io.micrometer.tracing.BaggageInScope;
import io.micrometer.tracing.Tracer;
import org.junit.Test;
import org.slf4j.MDC;
import org.springframework.messaging.MessageHeaders;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Demonstration of how session ID and customer ID are propagated through baggage
 * for both Spring Boot 2 and Spring Boot 3 compatibility.
 */
public class BaggagePropagationDemo extends MdcTest {

    private final TraceableProperties traceableProperties = new TraceableProperties();
    private final Tracer mockTracer = mock(Tracer.class);
    private final TraceableMdcAdapter adapter = new TraceableMdcAdapter(traceableProperties, mockTracer);

    @Test
    public void demonstrateBaggagePropagation() {
        System.out.println("=== Baggage Propagation Demo ===");

        // Step 1: Simulate incoming request with session and customer headers
        Map<String, Object> incomingHeaders = new HashMap<>();
        incomingHeaders.put(TraceableSupport.SESSION_ID_HEADER, "session-12345");
        incomingHeaders.put(TraceableSupport.CUSTOMER_ID_HEADER, "customer-67890");
        incomingHeaders.put(TraceableSupport.TRANSACTION_ID_HEADER, "transaction-abcdef");

        MessageHeaders messageHeaders = new MessageHeaders(incomingHeaders);

        System.out.println("Incoming headers:");
        System.out.println("  " + TraceableSupport.SESSION_ID_HEADER + ": " + incomingHeaders.get(TraceableSupport.SESSION_ID_HEADER));
        System.out.println("  " + TraceableSupport.CUSTOMER_ID_HEADER + ": " + incomingHeaders.get(TraceableSupport.CUSTOMER_ID_HEADER));

        // Mock baggage creation for demo
        BaggageInScope mockSessionBaggage = mock(BaggageInScope.class);
        BaggageInScope mockCustomerBaggage = mock(BaggageInScope.class);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.SESSION_ID_BAGGAGE), eq("session-12345")))
                .thenReturn(mockSessionBaggage);
        when(mockTracer.createBaggageInScope(eq(TraceableSupport.CUSTOMER_ID_BAGGAGE), eq("customer-67890")))
                .thenReturn(mockCustomerBaggage);

        // Step 2: Process the incoming request
        try (MDC.MDCCloseable closeable = adapter.from(messageHeaders)) {
            System.out.println("\nAfter processing incoming headers:");

            // Check MDC
            System.out.println("MDC contains:");
            System.out.println("  " + TraceableSupport.SESSION_ID_HEADER + ": " + MDC.get(TraceableSupport.SESSION_ID_HEADER));
            System.out.println("  " + TraceableSupport.CUSTOMER_ID_HEADER + ": " + MDC.get(TraceableSupport.CUSTOMER_ID_HEADER));

            // Verify MDC values
            assertThat(MDC.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("session-12345");
            assertThat(MDC.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("customer-67890");

            System.out.println("\n✅ MDC population working correctly!");
            System.out.println("✅ Baggage creation called on tracer!");
        }

        System.out.println("\n=== Demo Complete ===");
    }
}
