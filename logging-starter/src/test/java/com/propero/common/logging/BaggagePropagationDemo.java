package com.propero.common.logging;

import io.micrometer.tracing.Baggage;
import org.junit.Test;
import org.slf4j.MDC;
import org.springframework.messaging.MessageHeaders;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Demonstration of how session ID and customer ID are propagated through baggage
 * for both Spring Boot 2 and Spring Boot 3 compatibility.
 */
public class BaggagePropagationDemo extends MdcTest {

    private final TraceableProperties traceableProperties = new TraceableProperties();
    private final TraceableMdcAdapter adapter = new TraceableMdcAdapter(traceableProperties);

    @Test
    public void demonstrateBaggagePropagation() {
        System.out.println("=== Baggage Propagation Demo ===");
        
        // Step 1: Simulate incoming request with session and customer headers
        Map<String, Object> incomingHeaders = new HashMap<>();
        incomingHeaders.put(TraceableSupport.SESSION_ID_HEADER, "session-12345");
        incomingHeaders.put(TraceableSupport.CUSTOMER_ID_HEADER, "customer-67890");
        incomingHeaders.put(TraceableSupport.TRANSACTION_ID_HEADER, "transaction-abcdef");
        
        MessageHeaders messageHeaders = new MessageHeaders(incomingHeaders);
        
        System.out.println("Incoming headers:");
        System.out.println("  " + TraceableSupport.SESSION_ID_HEADER + ": " + incomingHeaders.get(TraceableSupport.SESSION_ID_HEADER));
        System.out.println("  " + TraceableSupport.CUSTOMER_ID_HEADER + ": " + incomingHeaders.get(TraceableSupport.CUSTOMER_ID_HEADER));
        
        // Step 2: Process the incoming request
        try (MDC.MDCCloseable closeable = adapter.from(messageHeaders)) {
            System.out.println("\nAfter processing incoming headers:");
            
            // Check MDC
            System.out.println("MDC contains:");
            System.out.println("  " + TraceableSupport.SESSION_ID_HEADER + ": " + MDC.get(TraceableSupport.SESSION_ID_HEADER));
            System.out.println("  " + TraceableSupport.CUSTOMER_ID_HEADER + ": " + MDC.get(TraceableSupport.CUSTOMER_ID_HEADER));
            
            // Check Baggage
            System.out.println("Baggage contains:");
            System.out.println("  " + TraceableSupport.SESSION_ID_BAGGAGE + ": " + Baggage.current().get(TraceableSupport.SESSION_ID_BAGGAGE));
            System.out.println("  " + TraceableSupport.CUSTOMER_ID_BAGGAGE + ": " + Baggage.current().get(TraceableSupport.CUSTOMER_ID_BAGGAGE));
            
            // Step 3: Simulate outgoing request
            Map<String, String> outgoingHeaders = new HashMap<>();
            adapter.to(outgoingHeaders::put);
            
            System.out.println("\nOutgoing headers (propagated):");
            System.out.println("  " + TraceableSupport.SESSION_ID_HEADER + ": " + outgoingHeaders.get(TraceableSupport.SESSION_ID_HEADER));
            System.out.println("  " + TraceableSupport.CUSTOMER_ID_HEADER + ": " + outgoingHeaders.get(TraceableSupport.CUSTOMER_ID_HEADER));
            System.out.println("  " + TraceableSupport.TRANSACTION_ID_HEADER + ": " + outgoingHeaders.get(TraceableSupport.TRANSACTION_ID_HEADER));
            
            // Verify propagation works
            assertThat(outgoingHeaders.get(TraceableSupport.SESSION_ID_HEADER)).isEqualTo("session-12345");
            assertThat(outgoingHeaders.get(TraceableSupport.CUSTOMER_ID_HEADER)).isEqualTo("customer-67890");
            
            System.out.println("\n✅ Baggage propagation working correctly!");
        }
        
        // Step 4: Verify cleanup
        System.out.println("\nAfter closing context:");
        System.out.println("Baggage contains:");
        System.out.println("  " + TraceableSupport.SESSION_ID_BAGGAGE + ": " + Baggage.current().get(TraceableSupport.SESSION_ID_BAGGAGE));
        System.out.println("  " + TraceableSupport.CUSTOMER_ID_BAGGAGE + ": " + Baggage.current().get(TraceableSupport.CUSTOMER_ID_BAGGAGE));
        
        assertThat(Baggage.current().get(TraceableSupport.SESSION_ID_BAGGAGE)).isNull();
        assertThat(Baggage.current().get(TraceableSupport.CUSTOMER_ID_BAGGAGE)).isNull();
        
        System.out.println("✅ Cleanup working correctly!");
        System.out.println("\n=== Demo Complete ===");
    }
}
