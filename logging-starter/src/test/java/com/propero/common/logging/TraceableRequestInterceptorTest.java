package com.propero.common.logging;

import feign.RequestTemplate;
import org.junit.Test;

import static org.assertj.core.api.Assertions.assertThat;

public class TraceableRequestInterceptorTest extends MdcTest {

    private final TraceableProperties traceableProperties = new TraceableProperties();
    private final TraceableRequestInterceptor interceptor = new TraceableRequestInterceptor(new TraceableMdcAdapter(traceableProperties, null));

    @Test
    public void apply() {
        TraceableTestSupport.initMdc(traceableProperties);
        RequestTemplate requestTemplate = new RequestTemplate();
        interceptor.apply(requestTemplate);
        TraceableTestSupport.getAllTraceableHeaders(traceableProperties)
                .forEach(header -> assertThat(requestTemplate.headers()).containsKey(header));
    }
}