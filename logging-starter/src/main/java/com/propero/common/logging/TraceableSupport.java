package com.propero.common.logging;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.val;
import org.slf4j.MDC;
import org.springframework.messaging.MessageHeaders;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@SuppressWarnings("WeakerAccess")
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TraceableSupport {
    // custom headers
    public static final String TRANSACTION_ID_HEADER = "x-transaction_id";
    public static final String CUSTOMER_ID_HEADER = "x-customer_id";
    public static final String FUNCTION_HEADER = "x-function";
    public static final String SOURCE_CALLER_HEADER = "x-source_caller";
    public static final String SUB_CALLER_HEADER = "x-sub_caller";
    public static final String SESSION_ID_HEADER = "x-session_id";
    
    // Spring Boot 3 W3C Trace Context headers
    public static final String TRACE_PARENT_HEADER = "traceparent";
    public static final String SESSION_ID_BAGGAGE = "sessionId";
    public static final String CUSTOMER_ID_BAGGAGE = "customerId";

    public static String getOrGenerateTraceId() {
        // First look for W3C traceparent
        return Optional.ofNullable(MDC.get(TRACE_PARENT_HEADER))
                .filter(traceParent -> traceParent.length() > 35)
                .map(traceParent -> traceParent.substring(3, 35))
                .orElseGet(
                        // otherwise look for the custom header or generate a new traceId
                        () -> Optional.ofNullable(MDC.get(TRANSACTION_ID_HEADER))
                                .orElseGet(TraceableSupport::generateTraceId)
                );
    }

    public static Optional<String> getSessionId() {
        return Optional.ofNullable(MDC.get(SESSION_ID_HEADER));
    }

    public static MDC.MDCCloseable generateAndSetNewTraceAndTransactionId() {
        return generateAndSetNewTraceAndTransactionId(null);
    }

    public static MDC.MDCCloseable generateAndSetNewTraceAndTransactionId(String prefix) {
        return setTraceAndTransactionId(generateTraceId(prefix));
    }

    public static MDC.MDCCloseable populateTraceAndTransactionId(String traceId) {
        return populateTraceAndTransactionId(traceId, null);
    }

    public static MDC.MDCCloseable populateTraceAndTransactionId(String traceId, String prefix) {
        if (Objects.isNull(traceId)) {
            return generateAndSetNewTraceAndTransactionId(prefix);
        } else {
            return setTraceAndTransactionId(traceId);
        }
    }

    public static MessageHeaders withGeneratedTransactionIdIfAbsent(MessageHeaders headers) {
        // Check for the standard W3C header first
        return extractTraceIdFromHeaders(headers)
                .map(traceId -> {
                    val values = new HashMap<>(headers);
                    values.put(TRANSACTION_ID_HEADER, traceId);
                    values.put(TRACE_PARENT_HEADER, generateTraceparentId(traceId));
                    return new MessageHeaders(values);
                }).orElseGet(() ->
                        // if the transaction ID is already present, return the headers as is
                        Optional.of(headers.containsKey(TRANSACTION_ID_HEADER))
                        .map(unused -> headers)
                        .orElseGet(() ->
                                // otherwise generate a new transaction ID
                                withTraceAndTransactionId(headers)));
    }

    static MDC.MDCCloseable setTraceAndTransactionId(String traceId) {
        String traceparentId = generateTraceparentId(traceId);
        MDC.put(TRACE_PARENT_HEADER, traceparentId);
        return MDC.putCloseable(TRANSACTION_ID_HEADER, traceId);
    }

    private static MessageHeaders withTraceAndTransactionId(MessageHeaders headers) {
        val values = new HashMap<>(headers);
        String traceId = generateTraceId();
        values.put(TRANSACTION_ID_HEADER, traceId);
        values.put(TRACE_PARENT_HEADER, generateTraceparentId(traceId) );
        return new MessageHeaders(values);
    }

    /**
     * Generates a traceparent header value from a trace ID.
     * It uses the W3C trace context format (using 00 version, random span, sampled flag)
     * @param traceId traceId to include in the traceparent header
     * @return traceparent header value
     */
    private static String generateTraceparentId(String traceId) {
        String spanId = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        return "00-" + traceId + "-" + spanId + "-01";
    }

    private static String generateTraceId(String prefix) {
        return ObjectUtils.isEmpty(prefix) ? generateTraceId() : prefix + "-" + generateTraceId();
    }

    private static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    private static Optional<String> extractTraceIdFromHeaders(Map<String, Object> headers) {
        return Optional.ofNullable(headers.get(TRACE_PARENT_HEADER))
                .filter(traceparent -> traceparent instanceof String && ((String) traceparent).length() > 35)
                .map(traceparent -> ((String) traceparent).substring(3, 35));
    }
}
