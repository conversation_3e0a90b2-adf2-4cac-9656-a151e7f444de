package com.propero.common.logging;

import io.micrometer.tracing.Baggage;
import io.micrometer.tracing.BaggageInScope;
import lombok.RequiredArgsConstructor;
import org.slf4j.MDC;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

@SuppressWarnings("WeakerAccess")
@RequiredArgsConstructor
public class TraceableMdcAdapter {

    private final TraceableProperties traceableProperties;

    public MDC.MDCCloseable from(Message<?> message) {
        return from(message.getHeaders());
    }

    public MDC.MDCCloseable from(MessageHeaders headers) {
        return from(headers, (messageHeaders, headerName) -> messageHeaders.get(headerName, String.class));
    }

    public MDC.MDCCloseable from(HttpServletRequest request, String prefix) {
        return from(request, HttpServletRequest::getHeader, prefix);
    }

    public MDC.MDCCloseable from(HttpHeaders headers, String prefix) {
        return from(headers, HttpHeaders::getFirst, prefix);
    }

    public <T> MDC.MDCCloseable from(T source, BiFunction<T, String, String> valueExtractor) {
        return from(source, valueExtractor, null);
    }

    private <T> MDC.MDCCloseable from(T source, BiFunction<T, String, String> valueExtractor, String prefix) {
        // Populate MDC with all traceable headers
        traceableProperties.getHeaders()
                .forEach(key -> Optional.ofNullable(valueExtractor.apply(source, key))
                        .ifPresent(value -> MDC.put(key, value)));

        // Get the MDC closeable for trace and transaction ID
        MDC.MDCCloseable mdcCloseable = TraceableSupport.populateTraceAndTransactionId(
                valueExtractor.apply(source, traceableProperties.getTraceIdHeader()), prefix);

        // Create baggage scopes for sessionId and customerId
        List<BaggageInScope> baggageScopes = new ArrayList<>();

        // Add sessionId to baggage if present
        Optional.ofNullable(valueExtractor.apply(source, TraceableSupport.SESSION_ID_BAGGAGE))
                .ifPresent(sessionId -> {
                    BaggageInScope sessionBaggage = Baggage.current()
                            .toBuilder()
                            .put(TraceableSupport.SESSION_ID_BAGGAGE, sessionId)
                            .build()
                            .makeCurrent();
                    baggageScopes.add(sessionBaggage);
                });

        // Add customerId to baggage if present
        Optional.ofNullable(valueExtractor.apply(source, TraceableSupport.CUSTOMER_ID_BAGGAGE))
                .ifPresent(customerId -> {
                    BaggageInScope customerBaggage = Baggage.current()
                            .toBuilder()
                            .put(TraceableSupport.CUSTOMER_ID_BAGGAGE, customerId)
                            .build()
                            .makeCurrent();
                    baggageScopes.add(customerBaggage);
                });

        // Return composite closeable that manages both MDC and baggage
        return new TraceableCloseable(mdcCloseable, baggageScopes);
    }

    public org.springframework.amqp.core.Message to(org.springframework.amqp.core.Message message) {
        MessageProperties messageProperties = message.getMessageProperties();
        to(messageProperties::setHeader);
        return message;
    }

    public Message<?> to(Message<?> message) {
        MessageBuilder<?> messageBuilder = MessageBuilder.withPayload(message.getPayload())
                .copyHeaders(message.getHeaders());
        to(messageBuilder::setHeader);
        return messageBuilder.build();
    }

    public void to(BiConsumer<String, String> consumer) {
        Optional.ofNullable(MDC.getCopyOfContextMap())
                .ifPresent(headers -> headers.forEach(consumer));
    }
}
