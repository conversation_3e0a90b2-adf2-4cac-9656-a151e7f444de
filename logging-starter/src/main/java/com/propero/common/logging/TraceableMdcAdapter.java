package com.propero.common.logging;

import io.micrometer.tracing.BaggageInScope;
import io.micrometer.tracing.Tracer;
import lombok.RequiredArgsConstructor;
import org.slf4j.MDC;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.Closeable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;



@SuppressWarnings("WeakerAccess")
@RequiredArgsConstructor
public class TraceableMdcAdapter {

    private final TraceableProperties traceableProperties;
    private final Tracer tracer;

    public MDC.MDCCloseable from(Message<?> message) {
        return from(message.getHeaders());
    }

    public MDC.MDCCloseable from(MessageHeaders headers) {
        return from(headers, (messageHeaders, headerName) -> messageHeaders.get(headerName, String.class));
    }

    public MDC.MDCCloseable from(HttpServletRequest request, String prefix) {
        return from(request, HttpServletRequest::getHeader, prefix);
    }

    public MDC.MDCCloseable from(HttpHeaders headers, String prefix) {
        return from(headers, HttpHeaders::getFirst, prefix);
    }

    public <T> MDC.MDCCloseable from(T source, BiFunction<T, String, String> valueExtractor) {
        return from(source, valueExtractor, null);
    }

    private <T> MDC.MDCCloseable from(T source, BiFunction<T, String, String> valueExtractor, String prefix) {
        // Populate MDC with all traceable headers
        traceableProperties.getHeaders()
                .forEach(key -> Optional.ofNullable(valueExtractor.apply(source, key))
                        .ifPresent(value -> MDC.put(key, value)));

        // Get the MDC closeable for trace and transaction ID
        MDC.MDCCloseable mdcCloseable = TraceableSupport.populateTraceAndTransactionId(
                valueExtractor.apply(source, traceableProperties.getTraceIdHeader()), prefix);

        // Create baggage scopes for sessionId and customerId (only if tracer is available)
        List<BaggageInScope> baggageScopes = new ArrayList<>();

        if (tracer != null) {
            // Add sessionId to baggage if present
            Optional.ofNullable(valueExtractor.apply(source, TraceableSupport.SESSION_ID_HEADER))
                    .ifPresent(sessionId -> {
                        BaggageInScope sessionBaggage = tracer.createBaggageInScope(
                                TraceableSupport.SESSION_ID_BAGGAGE, sessionId);
                        baggageScopes.add(sessionBaggage);
                    });

            // Add customerId to baggage if present
            Optional.ofNullable(valueExtractor.apply(source, TraceableSupport.CUSTOMER_ID_HEADER))
                    .ifPresent(customerId -> {
                        BaggageInScope customerBaggage = tracer.createBaggageInScope(
                                TraceableSupport.CUSTOMER_ID_BAGGAGE, customerId);
                        baggageScopes.add(customerBaggage);
                    });
        }

        // Return composite closeable that manages both MDC and baggage
        if (baggageScopes.isEmpty()) {
            // No baggage scopes to manage, return the original MDC closeable
            return mdcCloseable;
        } else {
            // Create a composite closeable that manages both MDC and baggage
            return new MDC.MDCCloseable() {
                @Override
                public void close() {
                    try {
                        // Close baggage scopes first
                        baggageScopes.forEach(BaggageInScope::close);
                    } finally {
                        // Then close MDC
                        if (mdcCloseable != null) {
                            mdcCloseable.close();
                        }
                    }
                }
            };
        }
    }

    public org.springframework.amqp.core.Message to(org.springframework.amqp.core.Message message) {
        MessageProperties messageProperties = message.getMessageProperties();
        to(messageProperties::setHeader);
        return message;
    }

    public Message<?> to(Message<?> message) {
        MessageBuilder<?> messageBuilder = MessageBuilder.withPayload(message.getPayload())
                .copyHeaders(message.getHeaders());
        to(messageBuilder::setHeader);
        return messageBuilder.build();
    }

    public void to(BiConsumer<String, String> consumer) {
        // Propagate MDC values
        Optional.ofNullable(MDC.getCopyOfContextMap())
                .ifPresent(headers -> headers.forEach(consumer));

        // Propagate baggage values as headers (only if tracer is available)
        if (tracer != null) {
            Optional.ofNullable(tracer.getBaggage(TraceableSupport.SESSION_ID_BAGGAGE).get())
                    .ifPresent(sessionId -> consumer.accept(TraceableSupport.SESSION_ID_HEADER, sessionId));
            Optional.ofNullable(tracer.getBaggage(TraceableSupport.CUSTOMER_ID_BAGGAGE).get())
                    .ifPresent(customerId -> consumer.accept(TraceableSupport.CUSTOMER_ID_HEADER, customerId));
        }
    }
}
