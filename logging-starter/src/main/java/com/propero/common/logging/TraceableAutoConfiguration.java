package com.propero.common.logging;

import io.micrometer.tracing.Tracer;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@EnableConfigurationProperties(TraceableProperties.class)
@AutoConfigureBefore(WebMvcAutoConfiguration.class)
@Import({FeignTraceableConfiguration.class, TraceableRabbitConfiguration.class, WebMvcTraceableConfiguration.class})
public class TraceableAutoConfiguration {

    @Bean
    TraceableMdcAdapter traceableMdcAdapter(TraceableProperties properties) {
        return new TraceableMdcAdapter(properties);
    }

    @Bean
    @ConditionalOnProperty(value = "logging.traceable.aspect.enabled", havingValue = "true", matchIfMissing = true)
    TraceableMethodAspect traceHeaderExtractorAspect(TraceableMdcAdapter traceableMdcAdapter) {
        return new TraceableMethodAspect(traceableMdcAdapter);
    }

    @Bean
    TraceableTaskDecorator traceableTaskDecorator() {
        return new TraceableTaskDecorator();
    }
}
