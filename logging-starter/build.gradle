dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework:spring-messaging'
    implementation 'org.springframework.amqp:spring-amqp'

    implementation 'com.google.guava:guava'
    implementation 'net.logstash.logback:logstash-logback-encoder'

    compileOnly 'org.springframework.boot:spring-boot-starter-web'
    compileOnly 'org.springframework.boot:spring-boot-starter-webflux'
    compileOnly 'org.springframework.boot:spring-boot-starter-amqp'
    compileOnly 'org.springframework.boot:spring-boot-starter-jetty'

    compileOnly 'io.github.openfeign:feign-slf4j'
    implementation group: 'io.micrometer', name: 'micrometer-tracing-bridge-otel', version: '1.5.1'

    annotationProcessor "org.springframework.boot:spring-boot-autoconfigure-processor"

    testCompile 'org.springframework.boot:spring-boot-starter-amqp'
    testCompile 'org.springframework.boot:spring-boot-starter-web'
    testCompile 'org.springframework.boot:spring-boot-starter-jetty'
    testCompile 'io.github.openfeign:feign-slf4j'
}
